.math-magic-highlight {
  outline: 2px solid #4CAF50 !important;
  background-color: rgba(76, 175, 80, 0.1) !important;
  transition: all 0.2s ease-in-out;
}

.math-magic-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 10000;
  cursor: crosshair;
  display: none;
}

.math-magic-selection {
  position: absolute;
  border: 2px solid #4CAF50;
  background: rgba(76, 175, 80, 0.1);
  pointer-events: none;
  z-index: 10001;
}

.math-magic-toolbar {
  position: fixed;
  background: white;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 10002;
  display: flex;
  gap: 8px;
}

.math-magic-toolbar button {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background: #4CAF50;
  color: white;
  cursor: pointer;
}

.math-magic-toolbar button:hover {
  background: #45a049;
}