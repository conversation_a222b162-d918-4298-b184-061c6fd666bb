<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- MathJax 配置和核心文件 -->
  <script src="lib/mathjax/config.js"></script>
  <script src="lib/mathjax/es5/tex-chtml.js" id="MathJax-script" async></script>
  <style>
    :root {
      --primary-color: #4361ee;
      --primary-hover: #3a56d4;
      --secondary-color: #4f5d75;
      --secondary-hover: #404b5e;
      --success-color: #2ecc71;
      --error-color: #e74c3c;
      --info-color: #3498db;
      --border-color: #e2e8f0;
      --bg-color: #ffffff;
      --text-color: #333333;
      --text-secondary: #666666;
      --radius-sm: 6px;
      --radius-md: 8px;
      --radius-lg: 12px;
      --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
      --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
      --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* 暗黑模式支持 */
    @media (prefers-color-scheme: dark) {
      :root {
        --primary-color: #4361ee;
        --primary-hover: #5a75f3;
        --secondary-color: #6c7a93;
        --secondary-hover: #7d8ba3;
        --border-color: #2d3748;
        --bg-color: #1a202c;
        --text-color: #e2e8f0;
        --text-secondary: #a0aec0;
      }
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      width: 380px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      max-height: 580px;
      overflow-y: auto;
      margin: 0;
      background-color: var(--bg-color);
      color: var(--text-color);
      transition: var(--transition);
    }

    .container {
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .btn {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: var(--radius-md);
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: var(--transition);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      box-shadow: var(--shadow-sm);
    }

    .btn-primary {
      background: var(--primary-color);
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: var(--primary-hover);
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: var(--secondary-color);
      color: white;
    }

    .btn-secondary:hover:not(:disabled) {
      background: var(--secondary-hover);
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
      box-shadow: none !important;
    }

    .btn-icon {
      font-size: 18px;
      line-height: 1;
    }

    .image-area {
      width: 100%;
      min-height: 150px;
      border: 2px dashed var(--border-color);
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      transition: var(--transition);
      background-color: rgba(0, 0, 0, 0.01);
    }

    .image-area.drag-over {
      border-color: var(--primary-color);
      background: rgba(67, 97, 238, 0.05);
    }

    .image-area img {
      max-width: 100%;
      max-height: 200px;
      object-fit: contain;
    }

    .image-area .placeholder {
      color: var(--text-secondary);
      text-align: center;
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }

    .placeholder-icon {
      font-size: 24px;
      opacity: 0.7;
    }

    .upload-btn {
      margin-top: 12px;
      padding: 8px 16px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 6px;
      transition: var(--transition);
      box-shadow: var(--shadow-sm);
    }

    .upload-btn:hover {
      background-color: var(--primary-hover);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .upload-btn:active {
      transform: translateY(0);
      box-shadow: var(--shadow-sm);
    }

    .upload-icon {
      font-size: 14px;
    }

    /* 应用信息样式 */
    .app-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
    }

    .app-name-container {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .app-icon {
      font-size: 16px;
    }

    .app-name {
      font-weight: 600;
      font-size: 14px;
      color: var(--primary-color);
    }

    .app-version {
      font-size: 11px;
      color: var(--text-secondary);
      opacity: 0.8;
    }

    .clear-btn {
      position: absolute;
      top: 8px;
      right: 8px;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.8);
      border: none;
      cursor: pointer;
      display: none;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      box-shadow: var(--shadow-sm);
      z-index: 10;
    }

    .clear-btn:hover {
      background: rgba(255, 255, 255, 1);
      transform: scale(1.1);
    }

    .image-area:hover .clear-btn {
      display: flex;
    }

    .file-input {
      width: 0.1px;
      height: 0.1px;
      opacity: 0;
      overflow: hidden;
      position: absolute;
      z-index: -1;
    }

    .status-message {
      padding: 12px;
      border-radius: var(--radius-md);
      font-size: 14px;
      display: none;
      position: fixed;
      bottom: 16px;
      left: 16px;
      right: 16px;
      z-index: 1000;
      transition: var(--transition);
      align-items: center;
      justify-content: center;
      box-shadow: var(--shadow-md);
    }

    .status-message.error {
      background: var(--error-color);
      color: white;
    }

    .status-message.success {
      background: var(--success-color);
      color: white;
    }

    .status-message.info {
      background: var(--info-color);
      color: white;
    }

    .result-card {
      border-radius: var(--radius-lg);
      overflow: hidden;
      box-shadow: var(--shadow-md);
      border: 1px solid var(--border-color);
      display: flex;
      flex-direction: column;
      transition: var(--transition);
      opacity: 0;
      transform: translateY(10px);
      background-color: var(--bg-color);
    }

    .result-card.show {
      opacity: 1;
      transform: translateY(0);
    }

    .result-header {
      padding: 14px 16px;
      border-bottom: 1px solid var(--border-color);
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: rgba(67, 97, 238, 0.05);
    }

    .latex-container {
      padding: 16px;
      overflow: auto;
      max-height: 200px;
    }

    .latex-content {
      font-family: 'JetBrains Mono', 'Fira Code', monospace;
      padding: 12px;
      background: rgba(0, 0, 0, 0.02);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-sm);
      white-space: pre-wrap;
      word-break: break-all;
      line-height: 1.5;
      font-size: 13px;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      padding: 12px 16px;
      border-top: 1px solid var(--border-color);
    }

    .action-button {
      flex: 1;
      padding: 10px;
      border: none;
      border-radius: var(--radius-md);
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transition: var(--transition);
    }

    .copy-button {
      background: var(--primary-color);
      color: white;
    }

    .copy-button:hover:not(:disabled) {
      background: var(--primary-hover);
      transform: translateY(-1px);
    }

    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    .shortcut-hint {
      font-size: 12px;
      color: var(--text-secondary);
      text-align: center;
      margin-top: 4px;
    }

    .history-button {
      background: var(--bg-color);
      border: 1px solid var(--border-color);
      color: var(--text-secondary);
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: var(--transition);
      border-radius: 20px;
      padding: 8px 14px;
      display: flex;
      align-items: center;
      gap: 6px;
      box-shadow: var(--shadow-sm);
      margin-bottom: 12px;
    }

    .history-button:hover {
      color: var(--primary-color);
      border-color: var(--primary-color);
      background-color: rgba(67, 97, 238, 0.05);
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }

    .history-button:active {
      transform: translateY(0);
      box-shadow: var(--shadow-sm);
    }

    .history-icon {
      font-size: 16px;
    }

    /* 历史记录模态框 */
    .history-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 2000;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      visibility: hidden;
      transition: var(--transition);
    }

    .history-modal.show {
      opacity: 1;
      visibility: visible;
    }

    .history-content {
      width: 90%;
      max-width: 350px;
      max-height: 80vh;
      background-color: var(--bg-color);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      transform: translateY(-20px);
      transition: var(--transition);
    }

    .history-modal.show .history-content {
      transform: translateY(0);
    }

    .history-header {
      padding: 16px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: rgba(67, 97, 238, 0.05);
    }

    .history-title {
      font-weight: 600;
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .history-title-icon {
      font-size: 18px;
      color: var(--primary-color);
    }

    .history-close {
      background: transparent;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: var(--text-secondary);
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      transition: var(--transition);
    }

    .history-close:hover {
      background-color: rgba(0, 0, 0, 0.05);
      color: var(--text-color);
      transform: rotate(90deg);
    }

    .history-list {
      flex: 1;
      overflow-y: auto;
      padding: 0;
      margin: 0;
      list-style: none;
    }

    .history-item {
      padding: 14px 16px;
      border-bottom: 1px solid var(--border-color);
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
      gap: 12px;
      position: relative;
    }

    .history-item:hover {
      background-color: rgba(67, 97, 238, 0.05);
    }

    .history-item:active {
      background-color: rgba(67, 97, 238, 0.1);
    }

    .history-item::after {
      content: '›';
      position: absolute;
      right: 16px;
      color: var(--text-secondary);
      font-size: 18px;
      opacity: 0;
      transform: translateX(-10px);
      transition: var(--transition);
    }

    .history-item:hover::after {
      opacity: 1;
      transform: translateX(0);
    }

    .history-item-preview {
      width: 48px;
      height: 48px;
      border-radius: var(--radius-sm);
      object-fit: cover;
      border: 1px solid var(--border-color);
      flex-shrink: 0;
      box-shadow: var(--shadow-sm);
      transition: var(--transition);
    }

    .history-item:hover .history-item-preview {
      transform: scale(1.05);
      box-shadow: var(--shadow-md);
    }

    .history-item-content {
      flex: 1;
      overflow: hidden;
      padding-right: 20px;
    }

    .history-item-latex {
      font-family: 'JetBrains Mono', 'Fira Code', monospace;
      font-size: 12px;
      color: var(--text-color);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 4px 8px;
      background-color: rgba(0, 0, 0, 0.02);
      border-radius: var(--radius-sm);
      border: 1px solid var(--border-color);
      margin-bottom: 6px;
    }

    .history-item-time {
      font-size: 11px;
      color: var(--text-secondary);
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .history-item-time::before {
      content: '📅';
      font-size: 12px;
    }

    .history-empty {
      padding: 40px 16px;
      text-align: center;
      color: var(--text-secondary);
      font-size: 14px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
    }

    .history-empty::before {
      content: '📄';
      font-size: 32px;
      opacity: 0.5;
    }
  </style>
</head>

<body>
  <div class="container">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
      <div class="app-info">
        <div class="app-name-container">
          <span class="app-icon">✂️</span>
          <span class="app-name">Math Formula to MathType</span>
        </div>
        <span class="app-version">v1.0</span>
      </div>
      <button id="historyBtn" class="history-button" title="历史记录">
        <span class="history-icon">📂</span>
        <span>历史记录</span>
      </button>
    </div>
    <button id="startCapture" class="btn btn-primary">
      <span class="btn-icon">✂️</span>截取数学公式
    </button>
    <div class="shortcut-hint">快捷键: Alt+Shift+C</div>

    <div class="image-area" id="imagePreview">
      <div class="placeholder">
        <span class="placeholder-icon">📷</span>
        <span>拖放图片到此处</span>
        <button class="upload-btn" id="uploadBtn" title="上传图片">
          <span class="upload-icon">⬆️</span>
          <span>选择图片</span>
        </button>
      </div>
      <img id="previewImage" style="display: none" />
      <button class="clear-btn" id="clearImage" title="清除图片">×</button>
    </div>

    <input type="file" id="fileInput" class="file-input" accept="image/*" tabindex="-1" />
    <button id="analyzeImage" class="btn btn-secondary" disabled>
      <span class="btn-icon">🔍</span>分析公式
    </button>

    <div id="status" class="status-message"></div>

    <!-- 结果显示区域 -->
    <div class="result-card" id="resultContainer" style="display: none">
      <div class="result-header">
        <span>识别结果</span>
      </div>

      <!-- LaTeX 源码显示区域 -->
      <div class="latex-container">
        <div class="latex-content" id="latexContent"></div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button class="action-button copy-button" id="copyLatex">
          <span class="btn-icon">📋</span>复制 LaTeX
        </button>
      </div>
    </div>
  </div>

  <!-- 历史记录模态框 -->
  <div id="historyModal" class="history-modal">
    <div class="history-content">
      <div class="history-header">
        <div class="history-title">
          <span class="history-title-icon">📂</span>
          <span>历史记录</span>
        </div>
        <button id="historyCloseBtn" class="history-close">×</button>
      </div>
      <ul id="historyList" class="history-list">
        <!-- 历史记录将通过JavaScript动态生成 -->
      </ul>
    </div>
  </div>

  <script src="popup.js"></script>
</body>

</html>