{"manifest_version": 3, "name": "Math Formula to MathType", "version": "1.1.0", "description": "Capture math formulas and paste as MathType in Word", "icons": {"16": "images/icon16.png", "48": "images/icon48.png", "128": "images/icon128.png"}, "permissions": ["activeTab", "scripting", "storage", "clipboardWrite", "notifications", "windows", "system.display", "tabs"], "commands": {"_execute_action": {"suggested_key": {"default": "Alt+Shift+M"}, "description": "打开插件弹窗"}, "capture_formula": {"suggested_key": {"default": "Alt+Shift+C"}, "description": "截取数学公式"}}, "host_permissions": ["https://aip.baidubce.com/*", "<all_urls>", "*://*/*"], "action": {"default_title": "Math Formula to MathType", "default_icon": {"16": "images/icon16.png", "48": "images/icon48.png", "128": "images/icon128.png"}, "default_popup": "popup.html"}, "content_scripts": [{"matches": ["<all_urls>"], "css": ["styles.css"], "js": ["lib/html2canvas.min.js", "content.js"], "run_at": "document_end"}], "background": {"service_worker": "background.js"}, "web_accessible_resources": [{"resources": ["lib/html2canvas.min.js", "lib/mathjax/es5/tex-chtml.js", "lib/mathjax/config.js", "lib/mathjax/output/chtml/fonts/woff-v2/*"], "matches": ["<all_urls>"]}]}